<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Management Table</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="Common.css">
    <style>
        body {
            font-family: var(--font-family-base);
            background-color: var(--background-primary);
            color: var(--text-primary);
        }
        
        .table-container {
            background-color: var(--background-secondary);
            border-radius: var(--radius-card);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }
        
        .table-header {
            background-color: var(--background-secondary);
            border-bottom: var(--border-width-sm) solid var(--border-color);
            padding: var(--space-md);
        }
        
        .custom-table {
            margin-bottom: 0;
            font-size: var(--font-size-0_9-rem);
        }
        
        .custom-table thead th {
            background-color: var(--tertiary-gray-light);
            color: var(--text-secondary);
            font-weight: var(--font-weight-semibold);
            font-size: var(--font-size-0_8-rem);
            border: none;
            padding: var(--space-sm);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            white-space: nowrap;
        }
        
        .custom-table tbody td {
            padding: var(--space-sm);
            border-top: var(--border-width-sm) solid var(--border-color-light);
            vertical-align: middle;
            font-size: var(--font-size-0_8-rem);
        }
        
        .custom-table tbody tr:hover {
            background-color: var(--background-hover-light);
        }
        
        .project-id {
            color: var(--primary-blue);
            font-weight: var(--font-weight-semibold);
            text-decoration: none;
        }
        
        .project-id:hover {
            color: var(--primary-teal);
            text-decoration: underline;
        }
        
        .progress-bar-container {
            width: 60px;
            height: 8px;
            background-color: var(--border-color-light);
            border-radius: var(--radius-sm);
            overflow: hidden;
        }
        
        .progress-bar-fill {
            height: 100%;
            background-color: var(--success-main);
            transition: width var(--transition-duration-base);
        }
        
        .progress-bar-fill.warning {
            background-color: var(--warning-main);
        }
        
        .progress-bar-fill.danger {
            background-color: var(--error-main);
        }
        
        .status-badge {
            padding: var(--space-xxs) var(--space-xs);
            border-radius: var(--radius-button-sm);
            font-size: var(--font-size-0_75-rem);
            font-weight: var(--font-weight-medium);
            text-align: center;
            min-width: 70px;
        }
        
        .status-active {
            background-color: var(--success-main);
            color: var(--text-white);
        }
        
        .status-pending {
            background-color: var(--warning-main);
            color: var(--text-primary);
        }
        
        .status-overdue {
            background-color: var(--error-main);
            color: var(--text-white);
        }
        
        .owner-avatar {
            width: 24px;
            height: 24px;
            border-radius: var(--radius-full);
            color: var(--text-white);
            font-weight: var(--font-weight-bold);
            font-size: var(--font-size-0_75-rem);
            line-height: 24px;
            text-align: center;
            margin-right: var(--space-xxs);
        }
        
        .milestone-dot {
            width: 8px;
            height: 8px;
            border-radius: var(--radius-full);
            margin-right: var(--space-xxs);
        }
        
        .milestone-active {
            background-color: var(--success-main);
        }
        
        .milestone-pending {
            background-color: var(--warning-main);
        }
        
        .milestone-overdue {
            background-color: var(--error-main);
        }
        
        .text-muted-custom {
            color: var(--text-color-light-1) !important;
        }
        
        .text-primary-custom {
            color: var(--text-primary) !important;
            font-weight: var(--font-weight-medium);
        }
        
        .text-success-custom {
            color: var(--success-main) !important;
        }
        
        .text-warning-custom {
            color: var(--warning-main) !important;
        }
        
        .text-danger-custom {
            color: var(--error-main) !important;
        }
        
        .action-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: var(--font-size-0_9-rem);
            padding: var(--space-xxs);
            margin: 0 2px;
            cursor: pointer;
            transition: color var(--transition-duration-fast);
        }
        
        .action-btn:hover {
            color: var(--primary-teal);
        }
        
        .table-responsive {
            border: none;
        }
        
        .compact-text {
            line-height: 1.2;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-3">
        <div class="row">
            <div class="col-12">
                <div class="table-container">
                    <!-- Table Header -->
                    <div class="table-header">
                        <div class="row align-items-center">
                            <div class="col-12 col-md-6">
                                <h6 class="mb-0 text-primary-custom">Projects</h6>
                                <small class="text-muted-custom">Active Projects • Project Groups • Public Projects</small>
                            </div>
                            <div class="col-12 col-md-6 mt-2 mt-md-0">
                                <div class="row">
                                    <div class="col-12 col-sm-8">
                                        <input type="text" class="form-control form-control-sm" placeholder="Search projects...">
                                    </div>
                                    <div class="col-12 col-sm-4 mt-2 mt-sm-0">
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-list me-1"></i>List
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i class="fas fa-list me-2"></i>List View</a></li>
                                                <li><a class="dropdown-item" href="#"><i class="fas fa-th me-2"></i>Grid View</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Responsive Table -->
                    <div class="table-responsive">
                        <table class="table custom-table">
                            <thead>
                                <tr>
                                    <th scope="col" class="d-none d-lg-table-cell">ID</th>
                                    <th scope="col">Project Name</th>
                                    <th scope="col" class="d-none d-md-table-cell">Recent</th>
                                    <th scope="col" class="d-none d-xl-table-cell">Tasks</th>
                                    <th scope="col" class="d-none d-lg-table-cell">%</th>
                                    <th scope="col" class="d-none d-md-table-cell">Start Date</th>
                                    <th scope="col" class="d-none d-lg-table-cell">End Date</th>
                                    <th scope="col" class="d-none d-xl-table-cell">Owner</th>
                                    <th scope="col">Status</th>
                                    <th scope="col" class="d-none d-lg-table-cell">Milestone</th>
                                    <th scope="col" class="d-none d-xl-table-cell">Tags</th>
                                    <th scope="col" class="d-none d-lg-table-cell">Created By</th>
                                    <th scope="col">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="d-none d-lg-table-cell">
                                        <a href="#" class="project-id">HCL-45</a>
                                    </td>
                                    <td>
                                        <div class="compact-text">
                                            <div class="text-primary-custom">ABC Project Plan</div>
                                            <div class="d-lg-none">
                                                <small class="text-muted-custom">HCL-45 • Access Project</small>
                                            </div>
                                            <div class="d-md-none mt-1">
                                                <small class="text-success-custom">63% • 04/06/2025 - 21/10/2025</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="d-none d-md-table-cell">
                                        <small class="text-muted-custom">No Issues</small>
                                    </td>
                                    <td class="d-none d-xl-table-cell">
                                        <span class="text-success-custom">10</span>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <div class="progress-bar-container">
                                            <div class="progress-bar-fill" style="width: 63%"></div>
                                        </div>
                                        <small class="text-muted-custom">63%</small>
                                    </td>
                                    <td class="d-none d-md-table-cell">
                                        <small class="compact-text">04/06/2025</small>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <small class="compact-text">21/10/2025<br><span class="text-success-custom">(74 days to go)</span></small>
                                    </td>
                                    <td class="d-none d-xl-table-cell">
                                        <div class="d-flex align-items-center">
                                            <div class="owner-avatar" style="background-color: var(--primary-teal);">RH</div>
                                            <small>Royana Hema</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-badge status-active">Active</span>
                                        <div class="d-xl-none mt-1">
                                            <small class="text-muted-custom">Royana Hema</small>
                                        </div>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <div class="d-flex align-items-center">
                                            <div class="milestone-dot milestone-active"></div>
                                            <small>0</small>
                                        </div>
                                    </td>
                                    <td class="d-none d-xl-table-cell">
                                        <small class="text-muted-custom">0%</small>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <div class="d-flex align-items-center">
                                            <div class="owner-avatar" style="background-color: var(--info-main);">RH</div>
                                            <small>Royana Hema</small>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="action-btn" title="View">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn" title="More">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="d-none d-lg-table-cell">
                                        <a href="#" class="project-id">HCL-47</a>
                                    </td>
                                    <td>
                                        <div class="compact-text">
                                            <div class="text-primary-custom">ABC Workstreams & Stories</div>
                                            <div class="d-lg-none">
                                                <small class="text-muted-custom">HCL-47</small>
                                            </div>
                                            <div class="d-md-none mt-1">
                                                <small class="text-warning-custom">0% • 04/06/2025 - 21/10/2025</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="d-none d-md-table-cell">
                                        <small class="text-muted-custom">No Issues</small>
                                    </td>
                                    <td class="d-none d-xl-table-cell">
                                        <span class="text-muted-custom">0</span>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <div class="progress-bar-container">
                                            <div class="progress-bar-fill warning" style="width: 0%"></div>
                                        </div>
                                        <small class="text-muted-custom">0%</small>
                                    </td>
                                    <td class="d-none d-md-table-cell">
                                        <small class="compact-text">04/06/2025</small>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <small class="compact-text">21/10/2025<br><span class="text-success-custom">(74 days to go)</span></small>
                                    </td>
                                    <td class="d-none d-xl-table-cell">
                                        <div class="d-flex align-items-center">
                                            <div class="owner-avatar" style="background-color: var(--secondary-teal-dark);">RH</div>
                                            <small>Royana Hema</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-badge status-active">Active</span>
                                        <div class="d-xl-none mt-1">
                                            <small class="text-muted-custom">Royana Hema</small>
                                        </div>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <div class="d-flex align-items-center">
                                            <div class="milestone-dot milestone-pending"></div>
                                            <small>0</small>
                                        </div>
                                    </td>
                                    <td class="d-none d-xl-table-cell">
                                        <small class="text-muted-custom">0%</small>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <div class="d-flex align-items-center">
                                            <div class="owner-avatar" style="background-color: var(--info-main);">RH</div>
                                            <small>Royana Hema</small>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="action-btn" title="View">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn" title="More">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="d-none d-lg-table-cell">
                                        <a href="#" class="project-id">HCL-30</a>
                                    </td>
                                    <td>
                                        <div class="compact-text">
                                            <div class="text-primary-custom">Auction</div>
                                            <div class="d-lg-none">
                                                <small class="text-muted-custom">HCL-30</small>
                                            </div>
                                            <div class="d-md-none mt-1">
                                                <small class="text-success-custom">100% • 05/06/2025 - 05/06/2025</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="d-none d-md-table-cell">
                                        <small class="text-muted-custom">No Issues</small>
                                    </td>
                                    <td class="d-none d-xl-table-cell">
                                        <span class="text-success-custom">1</span>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <div class="progress-bar-container">
                                            <div class="progress-bar-fill" style="width: 100%"></div>
                                        </div>
                                        <small class="text-success-custom">100%</small>
                                    </td>
                                    <td class="d-none d-md-table-cell">
                                        <small class="compact-text">05/06/2025</small>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <small class="compact-text">05/06/2025<br><span class="text-success-custom">(2 days to go)</span></small>
                                    </td>
                                    <td class="d-none d-xl-table-cell">
                                        <div class="d-flex align-items-center">
                                            <div class="owner-avatar" style="background-color: var(--tag-color-purple);">MC</div>
                                            <small>Mukul Gaur</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-badge status-active">Active</span>
                                        <div class="d-xl-none mt-1">
                                            <small class="text-muted-custom">Mukul Gaur</small>
                                        </div>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <div class="d-flex align-items-center">
                                            <div class="milestone-dot milestone-active"></div>
                                            <small>1</small>
                                        </div>
                                    </td>
                                    <td class="d-none d-xl-table-cell">
                                        <small class="text-muted-custom">0%</small>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <div class="d-flex align-items-center">
                                            <div class="owner-avatar" style="background-color: var(--tag-color-purple);">MC</div>
                                            <small>Mukul Gaur</small>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="action-btn" title="View">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn" title="More">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
